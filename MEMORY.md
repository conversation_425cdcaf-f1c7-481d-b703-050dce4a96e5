# 项目记忆银行

## 项目概览

这是一个使用 Flask 和 SQLAlchemy 构建的简单微博客 Web 应用。

### 主要功能
- 用户登录和身份验证
- 发布、查看、编辑和删除微博
- 为微博添加标签
- 按标签过滤微博
- 搜索微博内容和标签
- 随机浏览微博
- 文件上传功能(最大100MB)

### 技术栈
- **后端**: Python, Flask, SQLAlchemy
- **数据库**: SQLite
- **前端**: HTML, CSS, JavaScript

## 核心文件结构

```
.
├── app.py            # Flask 应用主文件
├── models.py         # SQLAlchemy 数据模型
├── requirements.txt  # Python 依赖
├── README.md         # 项目说明文档
├── CHANGELOG.md      # 变更日志
├── templates/        # HTML 模板
├── static/           # 静态文件 (CSS, JS, Images)
├── uploads/          # 文件上传目录
├── scripts/          # 项目脚本目录
└── MEMORY.md         # 项目记忆银行 (当前文件)
```

## 数据模型

### Post 模型
```python
class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    tags = db.Column(JSON, default=list)
    created_at = db.Column(db.DateTime, nullable=False, default=beijing_time)
```

### User 模型
```python
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
```

## 主要路由

- `@app.route('/')` - 主页，显示微博列表
- `@app.route('/login')` - 用户登录页面
- `@app.route('/logout')` - 用户登出
- `@app.route('/random')` - 随机浏览微博页面
- `@app.route('/tags')` - 标签页面

## 开发规范

1. 使用简体中文进行开发和注释
2. 修改代码后需同步更新 CHANGELOG.md
3. 采用最小化修改原则修复 Bug
4. 遵循 .clinerules 中的项目规范

## 常用脚本

- `./scripts/run.sh` - 本地开发环境启动
- `./scripts/deploy.sh` - 生产环境部署
- `./scripts/backup.py` - 数据库备份
- `./scripts/create_test_user.py` - 创建测试用户

## 测试账户

| 用户名 | 密码 | 说明 |
|--------|------|------|
| `admin` | `password` | 管理员测试账户 |

## 部署信息

- **服务器**: Ubuntu
- **用户**: `lighthouse`
- **主机**: `**************`
- **端口**: `22`
- **部署路径**: `/home/<USER>/flask/weibo`
- **Web 服务器**: Caddy (支持 HTTPS)
- **进程管理器**: Supervisor

## 最近变更

查看 CHANGELOG.md 获取详细的变更历史。

## 下一步计划

1. 继续完善现有功能
2. 优化用户体验
3. 增强系统稳定性
