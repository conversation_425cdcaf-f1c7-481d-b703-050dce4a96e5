# .clinerules/clineignore - Cline 忽略文件配置
# 此文件配置 Cline 在分析代码时应该忽略的文件和目录

# ===== Python 相关 =====
# Python 字节码文件和缓存
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd
.pytest_cache/
.coverage
htmlcov/

# Python 虚拟环境
venv/
.venv/
env/
ENV/
.env
.python-version

# Python 分发/打包文件
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===== 数据库文件 =====
# SQLite 数据库
*.db
*.sqlite
*.sqlite3
*.db-journal

# 数据库备份文件
*.sql
*.dump

# ===== 上传和用户生成内容 =====
uploads/
media/
static/uploads/

# ===== 日志文件 =====
*.log
logs/
log/
*.log.*

# ===== 临时文件和缓存 =====
tmp/
temp/
.tmp/
.cache/
.nox/
coverage/

# ===== 测试相关 =====
# 测试覆盖率报告
.nyc_output/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# ===== IDE 和编辑器文件 =====
# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 操作系统生成的文件 =====
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.fseventsd
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory
.Trash-*

# ===== 前端相关 =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 前端构建输出
dist/
build/
.next/
.nuxt/
.vuepress/dist

# ===== 配置和密钥文件 =====
# 环境变量和配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.ini
config.yaml
config.yml
secrets.json
*.key
*.pem
*.p12
*.pfx

# ===== 版本控制 =====
.git/
.gitignore
.gitattributes
.gitmodules

# ===== 其他工具配置文件 =====
.editorconfig
.flake8
.pylintrc
pyproject.toml
setup.cfg
tox.ini
.pre-commit-config.yaml

# ===== 项目特定文件 =====
# 备份文件
*.backup

# 内存和历史文件
MEMORY.md

# 部署脚本（通常不需要代码分析）
scripts/deploy.sh
scripts/amend.sh
static/css/tailwind/tailwind.min.css

# ===== Cline 相关 =====
.cline/
.clinerules/
