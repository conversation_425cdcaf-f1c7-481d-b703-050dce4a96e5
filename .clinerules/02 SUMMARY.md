# ROO 规则汇总

## 目录
1. [项目概览](#项目概览)
2. [数据模型](#数据模型)
3. [路由和API端点](#路由和API端点)
4. [前端模板](#前端模板)
5. [常见用例和工作流](#常见用例和工作流)
6. [部署信息](#部署信息)
7. [服务器信息](#服务器信息)
8. [项目脚本使用指南](#项目脚本使用指南)

---

## 项目概览

这是一个使用Flask和SQLAlchemy构建的简单微博客Web应用。

### 主要文件和目录

- [app.py](mdc:app.py) - Flask应用主文件，包含所有路由和主要功能逻辑
- [models.py](mdc:models.py) - SQLAlchemy数据模型定义
- [templates/](mdc:templates) - HTML模板目录
- [static/](mdc:static) - 静态资源文件(CSS, JavaScript等)
- [weibo.db](mdc:weibo.db) - SQLite数据库文件
- [requirements.txt](mdc:requirements.txt) - Python依赖库清单

### 功能特性

- 用户登录和身份验证
- 发布、查看、编辑和删除微博
- 为微博添加标签
- 按标签过滤微博
- 搜索微博内容和标签
- 随机浏览微博
- 文件上传功能(最大100MB)

---

## 数据模型

应用的数据模型定义在 [models.py](mdc:models.py) 文件中。

### 主要模型

#### Post 模型

微博内容的数据模型：

```python
class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    tags = db.Column(JSON, default=list)
    created_at = db.Column(db.DateTime, nullable=False, default=beijing_time)
```

- `id`: 主键
- `content`: 微博内容
- `tags`: JSON类型字段，存储标签列表
- `created_at`: 创建时间，默认为北京时间

#### User 模型

用户数据模型：

```python
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
```

- `id`: 主键
- `username`: 用户名
- `password_hash`: 密码的哈希值

### 辅助函数

- `beijing_time()`: 返回带北京时区调整的当前时间

---

## 路由和API端点

应用的所有路由和API端点都定义在 [app.py](mdc:app.py) 中。

### 主要页面路由

- `@app.route('/')` - 主页，显示微博列表，支持分页、标签过滤和关键词搜索
- `@app.route('/login')` - 用户登录页面
- `@app.route('/logout')` - 用户登出
- `@app.route('/random')` - 随机浏览微博页面
- `@app.route('/tags')` - 标签页面，显示所有标签及其使用次数

### 微博操作路由

- `@app.route('/post/create', methods=['POST'])` - 创建新微博
- `@app.route('/post/<int:post_id>/update', methods=['POST'])` - 更新微博
- `@app.route('/post/<int:post_id>/delete', methods=['POST'])` - 删除微博

### API端点

- `@app.route('/api/random_post')` - 获取随机微博的API
- `@app.route('/api/post/navigate')` - 导航上一条/下一条微博的API

### 文件上传

- `@app.route('/upload', methods=['POST'])` - 文件上传处理

### 身份验证

应用使用Flask的session机制管理用户登录状态，大多数路由都使用`@login_required`装饰器来保护，确保只有登录用户才能访问。

---

## 前端模板

应用使用Flask的Jinja2模板引擎渲染HTML页面。主要模板文件位于 [templates/](mdc:templates) 目录。

### 主要模板文件

- [templates/index.html](mdc:templates/index.html) - 主页模板，显示微博列表，包含发布微博的表单和微博展示区域
- [templates/login.html](mdc:templates/login.html) - 登录页面模板
- [templates/random.html](mdc:templates/random.html) - 随机浏览微博页面模板
- [templates/tags.html](mdc:templates/tags.html) - 标签页面模板，展示所有标签及其使用次数
- [templates/_edit_modal.html](mdc:templates/_edit_modal.html) - 编辑微博的模态窗口模板，被其他模板引用

### 静态资源

静态资源文件位于 [static/](mdc:static) 目录，包括：

- CSS样式文件
- JavaScript脚本
- 图标和其他媒体文件

### JavaScript功能

主要前端交互功能包括：

- 添加和删除标签
- AJAX微博发布和编辑
- 随机浏览微博的导航
- 文件上传预览和处理

---

## 常见用例和工作流

这个文件描述了应用的主要使用场景和操作流程。

### 用户认证流程

1. 用户访问应用，如未登录会被重定向到登录页面
2. 用户输入用户名和密码进行登录
3. 登录成功后，用户ID存储在session中，用户可以访问应用功能
4. 用户可以通过点击"退出"链接退出登录

### 微博发布流程

1. 用户在主页的文本区域输入微博内容
2. 用户可以添加标签（通过在文本中使用 `#标签名` 格式或使用标签输入框）
3. 用户点击"发布"按钮提交微博
4. 服务器处理请求，保存微博内容和标签到数据库
5. 页面自动刷新显示最新发布的微博

### 微博编辑流程

1. 用户点击微博旁边的"编辑"按钮
2. 弹出编辑模态窗口，显示微博当前内容和标签
3. 用户修改内容和标签
4. 用户点击"保存"按钮提交更改
5. 服务器更新数据库中的微博内容

### 随机浏览微博

1. 用户访问随机浏览页面（/random）
2. 系统随机选择一条微博显示给用户
3. 用户可以点击"上一条"或"下一条"按钮浏览其他微博

### 标签和搜索功能

1. 用户可以在主页上按标签筛选微博
2. 用户可以使用搜索框搜索微博内容或标签
3. 用户可以访问标签页面查看所有标签及其使用统计

### 文件上传

1. 用户可以通过点击上传按钮选择文件
2. 文件上传后存储在服务器的uploads目录中
3. 文件上传链接会自动插入到微博内容中

---

## 部署信息

本应用的部署通过脚本 [scripts/deploy.sh](mdc:scripts/deploy.sh) 完成。

### 服务器配置

根据 `deploy.sh` 脚本，服务器配置如下：

- **操作系统:** Ubuntu
- **用户:** `lighthouse`
- **主机:** `**************`
- **端口:** `22`
- **远程目录:** `/home/<USER>/flask/weibo`
- **容器化:** 已安装 Docker
- **Web 服务器/反向代理:** 使用 Caddy 支持 HTTPS (安装在宿主机上)

### 部署流程概述

1.  **创建远程目录**: 在服务器上创建 `/home/<USER>/flask/weibo`。
2.  **同步文件**: 使用 `rsync` 将项目文件同步到远程目录，排除 `.git`, `__pycache__`, `*.pyc`, `venv/`, `*.db`。
3.  **创建日志目录**: 在远程目录创建 `logs` 子目录。
4.  **复制 Supervisor 配置**: 将 `scripts/weibo.conf` 复制到服务器的 `/etc/supervisor/conf.d/` 并更新 Supervisor。
5.  **服务器端操作**:
    *   `cd` 进入远程目录。
    *   创建并激活 Python 虚拟环境 (`venv`)。
    *   使用 `pip install -r requirements.txt` 安装依赖。
    *   如果 `weibo.db` 不存在，运行 `python3 init_db.py` 初始化数据库。
    *   使用 `sudo supervisorctl restart weibo` 重启应用（如果 Supervisor 已配置），否则使用 `nohup python3 app.py` 后台启动。

---

## 服务器信息

- **操作系统:** Ubuntu
- **用户:** `lighthouse`
- **主机:** `**************`
- **端口:** `22`
- **容器化:** 已安装 Docker
- **进程管理器:** 使用 `pm2` 统一管理所有后台服务 (`blog`, `haobbs`, `weibo`)。
- **Web 服务器/反向代理:** 使用 Caddy 支持 HTTPS (安装在宿主机上)

### Caddy 配置

- Caddy 配置文件路径: `/etc/caddy/Caddyfile`
- 重启 Caddy 服务命令: `sudo systemctl reload caddy`
- 当前配置的域名:
    - `weibo.haoxueren.com` -> `localhost:5001`
    - `bbs.haoxueren.com` -> `localhost:5002`
    - `blog.haoxueren.com` -> `localhost:5003`

---

## 项目脚本使用指南

本项目的常用脚本都位于 [scripts](mdc:scripts) 目录中，以下是各脚本的详细说明：

### 🚀 应用运行脚本

#### [scripts/run.sh](mdc:scripts/run.sh)
本地开发环境启动脚本，功能包括：
- 自动创建和激活Python虚拟环境
- 安装项目依赖
- 初始化数据库（如果不存在）
- 检查并清理端口占用
- 启动Flask应用

使用方法：
```bash
./scripts/run.sh
```

### 🗄️ 数据库相关脚本

#### [scripts/init_db.py](mdc:scripts/init_db.py)
数据库初始化脚本，用于创建数据库表结构
```bash
python scripts/init_db.py
```

#### [scripts/backup.py](mdc:scripts/backup.py)
数据库备份脚本，将weibo.db备份到~/Documents/Backup目录
```bash
python scripts/backup.py
```

### 👥 用户管理脚本

#### [scripts/create_test_user.py](mdc:scripts/create_test_user.py)
测试用户创建脚本，用于创建默认的测试用户
```bash
python scripts/create_test_user.py
```

### 🚚 部署相关脚本

#### [scripts/deploy.sh](mdc:scripts/deploy.sh)
生产环境部署脚本，功能包括：
- 同步代码到远程服务器 (**************)
- 在服务器上安装依赖
- 配置Supervisor进程管理
- 启动/重启应用服务

服务器配置信息：
- 用户: lighthouse
- 主机: **************
- 部署路径: /home/<USER>/flask/weibo

使用方法：
```bash
./scripts/deploy.sh
```

#### [scripts/weibo.conf](mdc:scripts/weibo.conf)
Supervisor进程管理配置文件，用于在生产环境中管理Flask应用进程。

### 🔧 开发工具脚本

#### [scripts/amend.sh](mdc:scripts/amend.sh)
Git快捷操作脚本，执行以下操作：
- 查看当前状态
- 添加所有变更
- 修正最后一次提交
- 显示操作后状态

使用方法：
```bash
./scripts/amend.sh
```

#### [scripts/random.py](mdc:scripts/random.py)
随机微博查看脚本，功能包括：
- 从远程服务器同步数据库
- 随机获取一条微博记录
- 使用macOS系统对话框显示内容

使用方法：
```bash
python scripts/random.py
```

### 📝 使用建议

1. **本地开发**: 使用 `./scripts/run.sh` 启动开发环境
2. **生产部署**: 使用 `./scripts/deploy.sh` 部署到服务器
3. **数据备份**: 定期运行 `python scripts/backup.py` 备份数据
4. **用户管理**: 通过 `python scripts/create_test_user.py` 创建测试用户
5. **快速提交**: 使用 `./scripts/amend.sh` 进行Git操作

### ⚠️ 注意事项

- 确保脚本有执行权限：`chmod +x scripts/*.sh`
- 部署脚本需要配置SSH密钥访问远程服务器
- 备份脚本会在用户文档目录创建Backup文件夹
- random.py脚本仅适用于macOS系统（使用osascript）
