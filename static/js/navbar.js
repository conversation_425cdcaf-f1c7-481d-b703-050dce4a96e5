/**
 * 导航栏移动端菜单控制
 * 控制汉堡菜单的展开和收起
 */

(function() {
    'use strict';

    // 导航栏菜单控制类
    class NavbarMenuController {
        constructor() {
            this.mobileMenuButton = document.getElementById('mobileMenuButton');
            this.mobileMenu = document.getElementById('mobileMenu');
            this.mainNav = document.getElementById('mainNav');
            
            this.init();
        }

        init() {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.bindEvents());
            } else {
                this.bindEvents();
            }
        }

        bindEvents() {
            if (this.mobileMenuButton && this.mobileMenu) {
                // 汉堡菜单按钮点击事件
                this.mobileMenuButton.addEventListener('click', () => {
                    this.toggleMobileMenu();
                });

                // 点击菜单外部区域关闭菜单
                document.addEventListener('click', (e) => {
                    if (this.mobileMenu && this.mobileMenuButton) {
                        // 如果点击的不是菜单按钮或菜单内容，则关闭菜单
                        if (!this.mobileMenuButton.contains(e.target) && 
                            !this.mobileMenu.contains(e.target) && 
                            this.mobileMenu.classList.contains('block')) {
                            this.closeMobileMenu();
                        }
                    }
                });

                // 监听窗口大小变化，当屏幕变大时自动关闭移动端菜单
                window.addEventListener('resize', () => {
                    if (window.innerWidth >= 768) { // md断点
                        this.closeMobileMenu();
                    }
                });

                // 监听菜单项点击，点击后自动关闭菜单
                const menuLinks = this.mobileMenu.querySelectorAll('a');
                menuLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        this.closeMobileMenu();
                    });
                });
            }
        }

        // 切换移动端菜单显示状态
        toggleMobileMenu() {
            if (this.mobileMenu) {
                if (this.mobileMenu.classList.contains('hidden')) {
                    this.openMobileMenu();
                } else {
                    this.closeMobileMenu();
                }
            }
        }

        // 打开移动端菜单
        openMobileMenu() {
            if (this.mobileMenu) {
                this.mobileMenu.classList.remove('hidden');
                this.mobileMenu.classList.add('block');
                
                // 添加打开动画效果
                this.mobileMenu.style.opacity = '0';
                this.mobileMenu.style.transform = 'translate(-50%, -10px)';
                this.mobileMenu.style.transition = 'all 0.3s ease';
                
                // 触发重排
                this.mobileMenu.offsetHeight;
                
                // 应用最终状态
                this.mobileMenu.style.opacity = '1';
                this.mobileMenu.style.transform = 'translate(-50%, 0)';
            }
        }

        // 关闭移动端菜单
        closeMobileMenu() {
            if (this.mobileMenu) {
                this.mobileMenu.classList.remove('block');
                this.mobileMenu.classList.add('hidden');
                
                // 重置样式
                this.mobileMenu.style.opacity = '';
                this.mobileMenu.style.transform = '';
                this.mobileMenu.style.transition = '';
            }
        }
    }

    // 创建全局导航栏控制器实例
    window.navbarMenuController = new NavbarMenuController();

})();
