// 编辑笔记相关变量
const editModal = document.getElementById('editModal');
const editForm = document.getElementById('editForm');
const editTagInput = document.getElementById('editTagInput');
const editTagsContainer = document.getElementById('editTagsContainer');
const editTagsField = document.getElementById('editTagsField');
let currentPostId = null;
let editTagManager; // 编辑模态框的标签管理器

function showEditModal() {
    document.body.style.overflow = 'hidden'; // 防止背景滚动
    editModal.classList.add('show');
}

function hideEditModal() {
    document.body.style.overflow = '';
    editModal.classList.remove('show');
    // 清空表单数据
    editForm.reset();
    editTagManager.clearTags();
    currentPostId = null;
}

// 设置当前时间到时间字段
function setCurrentTime() {
    const now = new Date();
    // 获取本地时间并格式化为 datetime-local 要求的格式
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    const formattedTime = `${year}-${month}-${day}T${hours}:${minutes}`;
    document.getElementById('editPostTime').value = formattedTime;
}

document.addEventListener('DOMContentLoaded', function () {
    // 创建编辑笔记的标签管理器
    editTagManager = new TagManager({
        tagInput: editTagInput,
        tagsContainer: editTagsContainer,
        tagsField: editTagsField
    });

    // 发布笔记的标签管理器现在由 PostModalController 处理
    
    // 初始化编辑模态框事件
    editModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', hideEditModal);
    });

    // Now按钮事件监听器
    const setCurrentTimeBtn = document.getElementById('setCurrentTimeBtn');
    if (setCurrentTimeBtn) {
        setCurrentTimeBtn.addEventListener('click', setCurrentTime);
    }

    // 点击模态框背景时关闭
    editModal.addEventListener('click', function (event) {
        // 仅允许通过关闭按钮关闭对话框，不再响应点击背景关闭
        if (event.target.classList.contains('close-button')) {
            hideEditModal();
        }
    });

    // 搜索高亮功能已移至 search.js 模块

    // 搜索功能已移至 search.js 模块

    // 发布按钮点击事件已移至 _post_modal.html 中的 PostModalController 类处理
    // 避免重复绑定事件监听器

    // 表单提交处理已移至 _post_modal.html 中的 PostModalController 类处理

    // 删除微博模态框控制
    const deleteModal = document.getElementById('deleteConfirmModal');
    const deleteForm = document.getElementById('deletePostForm');
    const modalBody = deleteModal.querySelector('.modal-body');

    function showDeleteModal() {
        deleteModal.classList.add('show');
    }

    function hideDeleteModal() {
        deleteModal.classList.remove('show');
    }

    // 关闭删除模态框事件
    deleteModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', hideDeleteModal);
    });

    deleteModal.addEventListener('click', function (event) {
        if (event.target === deleteModal) {
            hideDeleteModal();
        }
    });

    // 事件委托处理删除和编辑操作
    document.addEventListener('click', function (event) {
        const target = event.target;

        // 删除笔记处理
        if (target.classList.contains('delete-post')) {
            const postId = target.getAttribute('data-post-id');
            modalBody.textContent = '确定要删除这条笔记吗？';
            showDeleteModal();

            const confirmDeleteBtn = deleteModal.querySelector('.modal-footer .btn-danger');
            confirmDeleteBtn.onclick = function (e) {
                e.preventDefault();
                fetch(`/post/${postId}/delete`, {
                    method: 'POST'
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    const postCard = target.closest('.card');
                    postCard.remove();
                    hideDeleteModal();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除笔记失败，请重试');
                    hideDeleteModal();
                });
            };
        }

        // 编辑笔记处理
        if (target.classList.contains('edit-post')) {
            const postId = target.getAttribute('data-post-id');
            currentPostId = postId; // 设置当前编辑的微博ID

            // 通过API获取笔记的原始内容
            fetch(`/api/post/${postId}`)
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const post = data.post;

                        // 填充表单 - 使用原始Markdown内容
                        editForm.querySelector('textarea').value = post.content;

                        // 设置标签
                        editTagManager.setTags(post.tags || []);

                        // 设置发布时间
                        const formattedTime = post.created_at.replace(' ', 'T');
                        document.getElementById('editPostTime').value = formattedTime;

                        // 显示编辑对话框
                        showEditModal();
                    } else {
                        alert('获取笔记内容失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取笔记内容失败，请重试');
                });

            // 处理表单提交
            editForm.onsubmit = function (e) {
                e.preventDefault();
                
                if (!currentPostId) {
                    alert('编辑失败：未找到笔记ID');
                    return;
                }
                
                // 检查并添加输入框中未确认的标签
                editTagManager.processRemainingTag();
                
                // 创建 FormData 对象
                const formData = new FormData(this);
                
                // 手动将更新后的标签数组添加到 formData
                formData.append('tags', JSON.stringify(editTagManager.getTags()));
                
                // 获取并添加发布时间
                const editPostTime = document.getElementById('editPostTime').value;
                if (editPostTime) {
                    formData.append('created_at', editPostTime);
                }
                
                fetch(`/post/${currentPostId}/update`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 更新页面上的微博内容
                        const postContent = document.getElementById(`content-${currentPostId}`);
                        
                        // 检查元素是否存在，避免 TypeError
                        if (postContent) {
                            const postCard = postContent.closest('.card');
                            const tagsContainer = postCard.querySelector('.tags-container');
                            const postTimeElement = postCard.querySelector('.post-time');
                            
                            // 更新内容 - 使用后端返回的渲染后内容
                            postContent.innerHTML = data.post.rendered_content;
                            
                            // 直接使用后端返回的格式化时间
                            postTimeElement.textContent = data.post.created_at;
                            
                            // 更新标签
                            if (tagsContainer) {
                                tagsContainer.innerHTML = '';
                                const currentTag = new URLSearchParams(window.location.search).get('tag');
                                editTagManager.getTags().forEach(tag => {
                                    const tagElement = document.createElement('a');
                                    tagElement.href = `/?tag=${encodeURIComponent(tag)}`;
                                    tagElement.className = 'note-tag ' + (currentTag === tag ? 'note-tag--active' : 'note-tag--normal');
                                    
                                    // 添加标签文本
                                    const tagText = document.createElement('span');
                                    tagText.className = 'tag-text';
                                    tagText.textContent = tag;
                                    tagElement.appendChild(tagText);
                                    
                                    // 如果是当前激活的标签，添加指示器
                                    if (currentTag === tag) {
                                        const indicator = document.createElement('span');
                                        indicator.className = 'tag-indicator';
                                        tagElement.appendChild(indicator);
                                    }
                                    
                                    tagsContainer.appendChild(tagElement);
                                });
                            }
                        } else {
                            console.error('未找到ID为content-' + currentPostId + '的元素');
                            // 如果找不到元素，刷新页面以显示更新后的内容
                            location.reload();
                        }
                        
                        hideEditModal();
                    } else {
                        alert('编辑失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('编辑失败，请重试');
                });
            };
        }
    });
});
