document.addEventListener('DOMContentLoaded', function () {
    const postDisplayArea = document.getElementById('post-display-area');
    const randomAgainButton = document.getElementById('random-again-btn');
    
    // 编辑笔记相关元素
        const editModal = document.getElementById('editModal');
        const editForm = document.getElementById('editForm');
        const editTagInput = document.getElementById('editTagInput');
        const editTagsContainer = document.getElementById('editTagsContainer');
        const editTagsField = document.getElementById('editTagsField');
        let currentPostId = null;
        let editTagManager = null; // 编辑模态框的标签管理器
    
    // 删除笔记相关元素
    const deleteModal = document.getElementById('deleteConfirmModal');
    const deleteForm = document.getElementById('deletePostForm');
    
    // 显示编辑模态框
    function showEditModal() {
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        editModal.classList.add('show');
    }
    
    // 隐藏编辑模态框
    function hideEditModal() {
        document.body.style.overflow = '';
        editModal.classList.remove('show');
        // 清空表单数据
        editForm.reset();
        editTagManager.clearTags();
        currentPostId = null;
    }

    // 设置当前时间到时间字段
    function setCurrentTime() {
        const now = new Date();
        // 获取本地时间并格式化为 datetime-local 要求的格式
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        const formattedTime = `${year}-${month}-${day}T${hours}:${minutes}`;
        document.getElementById('editPostTime').value = formattedTime;
    }
    
    // 显示删除确认模态框
    function showDeleteModal() {
        deleteModal.classList.add('show');
    }
    
    // 隐藏删除确认模态框
    function hideDeleteModal() {
        deleteModal.classList.remove('show');
    }
    
    // 初始化标签管理器
    editTagManager = new TagManager({
        tagInput: editTagInput,
        tagsContainer: editTagsContainer,
        tagsField: editTagsField
    });

    function updatePostDisplay(postData) {
        if (!postData) {
            postDisplayArea.innerHTML = `
                <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in">
                    <div class="text-base leading-relaxed text-gray-500 text-center italic">加载笔记失败或没有笔记。</div>
                </div>`;
            return;
        }

        let tagsHtml = '';
        if (postData.tags && postData.tags.length > 0) {
             // 生成标签的 HTML，使用重构后的样式
             postData.tags.forEach(tag => {
                tagsHtml += `
                    <a href="/?tag=${encodeURIComponent(tag)}"
                       class="group relative inline-flex items-center px-3 py-1.5 text-xs font-medium no-underline rounded-full transition-all duration-300 ease-out bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 border border-indigo-200 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 hover:border-indigo-300 hover:scale-105 hover:shadow-md hover:shadow-indigo-500/10 hover:-translate-y-0.5">
                        <span class="relative z-10">${escapeHtml(tag)}</span>
                        <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    </a>`;
             });
        } else {
             tagsHtml = `
                <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-400 bg-gray-100 rounded-full border border-gray-200">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    无标签
                </span>`;
        }


        postDisplayArea.innerHTML = `
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in md:p-6" data-post-id="${postData.id}">
                 <div>
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200 md:flex-col md:items-start md:gap-2">
                        <span class="text-sm text-gray-500 font-medium md:order-2 md:text-xs">${postData.created_at}</span>
                        <div class="flex gap-2 md:order-1 md:w-full md:justify-end">
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post" data-post-id="${postData.id}">编辑</button>
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post" data-post-id="${postData.id}">删除</button>
                        </div>
                    </div>
                    <div class="text-base leading-relaxed text-gray-800 mb-8 break-words markdown-content md:text-sm" id="content-${postData.id}">${postData.rendered_content || escapeHtml(postData.content)}</div>
                 </div>
                 <div class="flex flex-wrap gap-2 mt-6 pt-4 border-t border-gray-200">
                    ${tagsHtml}
                </div>
            </div>`;
    }

    // 防止 XSS
    function escapeHtml(unsafe) {
        if (unsafe === null || typeof unsafe === 'undefined') {
            return '';
        }
        return unsafe
             .replace(/&/g, "&amp;")
             .replace(/</g, "&lt;")
             .replace(/>/g, "&gt;")
             .replace(/"/g, "&quot;")
             .replace(/'/g, "&#039;");
    }

    function fetchPost(url) {
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    // 尝试解析错误信息
                    return response.json().then(err => { throw new Error(err.message || `HTTP error ${response.status}`) });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updatePostDisplay(data.post);
                } else {
                    console.error('API Error:', data.message);
                    alert('加载笔记失败: ' + data.message);
                    // 可以选择显示错误信息，或者禁用按钮等
                     updatePostDisplay(null); // 显示错误状态
                }
            })
            .catch(error => {
                console.error('Fetch Error:', error);
                alert('网络错误或服务器无法响应: ' + error.message);
                 updatePostDisplay(null); // 显示错误状态
            });
    }

    // 再来一条按钮事件
    randomAgainButton.addEventListener('click', function () {
        fetchPost('/api/random_post'); // 调用获取随机笔记的 API
    });
    
    // 初始化模态框事件
    if (editModal) {
        editModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', hideEditModal);
        });
        
        // 初始化时隐藏模态框
        editModal.classList.remove('show');
    }

    // Now按钮事件监听器
    const setCurrentTimeBtn = document.getElementById('setCurrentTimeBtn');
    if (setCurrentTimeBtn) {
        setCurrentTimeBtn.addEventListener('click', setCurrentTime);
    }
    
    // 点击编辑模态框背景时关闭
    editModal.addEventListener('click', function (event) {
        // 仅允许通过关闭按钮关闭对话框，不再响应点击背景关闭
        if (event.target.classList.contains('close-button')) {
            hideEditModal();
        }
    });
    
    // 删除确认模态框关闭事件
    deleteModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', hideDeleteModal);
    });
    
    // 点击删除模态框背景时关闭
    deleteModal.addEventListener('click', function (event) {
        if (event.target === deleteModal) {
            hideDeleteModal();
        }
    });
    
    // 使用事件委托处理编辑和删除按钮点击
    document.addEventListener('click', function (event) {
        const target = event.target;
        
        // 编辑笔记处理
        if (target.classList.contains('edit-post')) {
            const postId = target.getAttribute('data-post-id');
            currentPostId = postId; // 设置当前编辑的笔记ID

            // 通过API获取笔记的原始内容
            fetch(`/api/post/${postId}`)
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const post = data.post;

                        // 填充表单 - 使用原始Markdown内容
                        const textarea = editForm.querySelector('textarea');
                        if (textarea) {
                            textarea.value = post.content;
                        }

                        // 设置标签
                        if (editTagManager) {
                            editTagManager.setTags(post.tags || []);
                        }

                        // 设置发布时间
                        const timeInput = document.getElementById('editPostTime');
                        if (timeInput) {
                            const formattedTime = post.created_at.replace(' ', 'T');
                            timeInput.value = formattedTime;
                        }

                        // 显示编辑对话框
                        showEditModal();
                    } else {
                        alert('获取笔记内容失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取笔记内容失败，请重试');
                });
            
            // 处理表单提交
            editForm.onsubmit = function (e) {
                e.preventDefault();
                
                if (!currentPostId) {
                    alert('编辑失败：未找到笔记ID');
                    return;
                }
                
                // 检查并添加输入框中未确认的标签
                editTagManager.processRemainingTag();
                
                // 创建 FormData 对象
                const formData = new FormData(this);
                
                // 手动将更新后的标签数组添加到 formData
                formData.append('tags', JSON.stringify(editTagManager.getTags()));
                
                // 获取并添加发布时间
                const editPostTime = document.getElementById('editPostTime').value;
                if (editPostTime) {
                    formData.append('created_at', editPostTime);
                }
                
                fetch(`/post/${currentPostId}/update`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 更新页面上的笔记内容
                        const postContent = document.getElementById(`content-${currentPostId}`);
                        const postCard = postContent.closest('div[data-post-id]');
                        // 查找标签容器 - 使用更精确的选择器
                        let tagsContainer = postCard.querySelector('.flex.flex-wrap.gap-2.mt-6.pt-4.border-t.border-gray-200');
                        // 如果没有找到标签容器，创建一个
                        if (!tagsContainer) {
                            tagsContainer = document.createElement('div');
                            tagsContainer.className = 'flex flex-wrap gap-2 mt-6 pt-4 border-t border-gray-200';
                            postCard.appendChild(tagsContainer);
                        }
                        const postTimeElement = postCard.querySelector('.text-sm.text-gray-500.font-medium');

                        // 更新内容 - 使用后端返回的渲染后内容
                        postContent.innerHTML = data.post.rendered_content;

                        // 直接使用后端返回的格式化时间
                        postTimeElement.textContent = data.post.created_at;

                        // 更新标签 - 使用Tailwind CSS类
                        tagsContainer.innerHTML = '';
                        const tags = editTagManager.getTags();
                        if (tags.length > 0) {
                            tags.forEach(tag => {
                                const tagElement = document.createElement('a');
                                tagElement.href = `/?tag=${encodeURIComponent(tag)}`;
                                tagElement.className = 'group relative inline-flex items-center px-3 py-1.5 text-xs font-medium no-underline rounded-full transition-all duration-300 ease-out bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 border border-indigo-200 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 hover:border-indigo-300 hover:scale-105 hover:shadow-md hover:shadow-indigo-500/10 hover:-translate-y-0.5';
                                tagElement.innerHTML = `
                                    <span class="relative z-10">${escapeHtml(tag)}</span>
                                    <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                                `;
                                tagsContainer.appendChild(tagElement);
                            });
                        } else {
                            // 无标签提示
                            const noTagElement = document.createElement('span');
                            noTagElement.className = 'inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-400 bg-gray-100 rounded-full border border-gray-200';
                            noTagElement.innerHTML = `
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                无标签
                            `;
                            tagsContainer.appendChild(noTagElement);
                        }

                        hideEditModal();
                    } else {
                        alert('编辑失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('编辑失败，请重试');
                });
            };
        }
        
        // 删除笔记处理
        if (target.classList.contains('delete-post')) {
            const postId = target.getAttribute('data-post-id');
            showDeleteModal();
            
            const confirmDeleteBtn = deleteModal.querySelector('button[type="submit"]');
            confirmDeleteBtn.onclick = function (e) {
                e.preventDefault();
                fetch(`/post/${postId}/delete`, {
                    method: 'POST'
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    hideDeleteModal();
                    // 删除成功后获取新的随机笔记
                    fetchPost('/api/random_post');
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除笔记失败，请重试');
                    hideDeleteModal();
                });
            };
        }
    });

});
