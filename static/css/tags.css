/* 公共标签样式 */

/* 基础标签样式 */
.note-tag {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    border-radius: 9999px;
    transition: all 0.3s ease;
    white-space: nowrap;
    cursor: pointer;
}

/* 标签文本样式 */
.note-tag .tag-text {
    position: relative;
    z-index: 10;
}

/* 普通状态标签样式 */
.note-tag--normal {
    background: linear-gradient(135deg, #e0e7ff 0%, #ede9fe 100%);
    color: #4f46e5;
    border: 1px solid #c7d2fe;
}

/* 激活状态标签样式 */
.note-tag--active {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.25), 0 2px 4px -1px rgba(99, 102, 241, 0.1);
    transform: scale(1.05);
}

/* 悬停状态标签样式 */
.note-tag--normal:hover {
    background: linear-gradient(135deg, #c7d2fe 0%, #ddd6fe 100%);
    color: #4338ca;
    border-color: #a5b4fc;
    transform: scale(1.05);
    box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.1), 0 2px 4px -1px rgba(99, 102, 241, 0.05);
    translate: 0 -0.5px;
}

/* 标签删除按钮样式 */
.note-tag .remove-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1rem;
    height: 1rem;
    margin-left: 0.25rem;
    font-size: 0.75rem;
    font-weight: bold;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    transition: all 0.2s ease;
}

.note-tag .remove-tag:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 激活状态标签的指示器 */
.note-tag--active .tag-indicator {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background: #fbbf24;
    border-radius: 50%;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 标签容器样式 */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* 标签输入区域样式 */
.tags-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    transition: border-color 0.3s ease;
}

.tags-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 标签反馈样式 */
.tag-feedback {
    position: absolute;
    bottom: -1.5rem;
    left: 0;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    z-index: 10;
    transition: all 0.3s ease;
}

.tag-feedback--success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.tag-feedback--warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.tag-feedback--error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.tag-feedback--info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}
