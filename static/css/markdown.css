/* Markdown内容公共样式 */

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #1f2937;
}

.markdown-content h1 { font-size: 1.5em; }
.markdown-content h2 { font-size: 1.3em; }
.markdown-content h3 { font-size: 1.1em; }

/* 段落样式 */
.markdown-content p {
    margin: 8px 0;
    line-height: 1.5;
}

/* 粗体样式 */
.markdown-content strong {
    font-weight: 600;
    color: #1f2937;
}

/* 斜体样式 */
.markdown-content em {
    font-style: italic;
}

/* 行内代码样式 */
.markdown-content code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e11d48;
}

/* 代码块样式 */
.markdown-content pre {
    background: #f8fafc;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid #e2e8f0;
}

.markdown-content pre code {
    background: none;
    padding: 0;
    color: #374151;
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.markdown-content ul {
    list-style-type: disc;
}

.markdown-content ol {
    list-style-type: decimal;
}

.markdown-content li {
    margin: 4px 0;
}

/* 链接样式 */
.markdown-content a {
    color: #6366f1;
    text-decoration: underline;
}

.markdown-content a:hover {
    color: #4f46e5;
}

/* 引用块样式 */
.markdown-content blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 16px;
    margin: 12px 0;
    color: #6b7280;
}

/* 表格样式 */
.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #e5e7eb;
    padding: 8px 12px;
    text-align: left;
}

.markdown-content th {
    background: #f8fafc;
    font-weight: 600;
}
