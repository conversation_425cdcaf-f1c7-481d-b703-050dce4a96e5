<!-- 导航栏 - 使用 Tailwind CSS 重构版本 -->
<div class="fixed top-0 left-1/2 transform -translate-x-1/2 z-50 bg-white border-b border-gray-200 shadow-sm transition-all duration-300 flex items-center justify-between w-full max-w-4xl px-8 py-4 min-h-[60px] md:px-4 md:py-3 md:min-h-[50px]" style="background-color: rgba(255, 255, 255, 0.98); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px);">
    <!-- 左侧：主导航标签页和汉堡菜单按钮 -->
    <div class="flex items-center">
        <!-- 汉堡菜单按钮（移动端） -->
        <button id="mobileMenuButton" class="md:hidden mr-3 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
        
        <!-- 主导航标签页（桌面端默认显示，移动端默认隐藏） -->
        <nav id="mainNav" class="hidden md:flex items-center gap-2 md:gap-1" role="tablist">
            <!-- 主页标签 -->
            <a href="{{ url_for('index') }}"
               class="group relative text-sm font-medium no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out
                      {% if active_page == 'index' %}
                        text-white bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25 scale-105
                      {% else %}
                        text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:scale-105 hover:shadow-md
                      {% endif %}
                      md:px-3 md:py-2 md:text-xs"
               role="tab"
               aria-selected="{% if active_page == 'index' %}true{% else %}false{% endif %}">
                <span class="relative z-10">主页</span>
                {% if active_page != 'index' %}
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                {% endif %}
            </a>

            <!-- 标签页标签 -->
            <a href="{{ url_for('tags') }}"
               class="group relative text-sm font-medium no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out
                      {% if active_page == 'tags' %}
                        text-white bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25 scale-105
                      {% else %}
                        text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:scale-105 hover:shadow-md
                      {% endif %}
                      md:px-3 md:py-2 md:text-xs"
               role="tab"
               aria-selected="{% if active_page == 'tags' %}true{% else %}false{% endif %}">
                <span class="relative z-10">标签</span>
                {% if active_page != 'tags' %}
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                {% endif %}
            </a>

            <!-- 随机页标签 -->
            <a href="{{ url_for('random_post_page') }}"
               class="group relative text-sm font-medium no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out
                      {% if active_page == 'random' %}
                        text-white bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25 scale-105
                      {% else %}
                        text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:scale-105 hover:shadow-md
                      {% endif %}
                      md:px-3 md:py-2 md:text-xs"
               role="tab"
               aria-selected="{% if active_page == 'random' %}true{% else %}false{% endif %}">
                <span class="relative z-10">随机</span>
                {% if active_page != 'random' %}
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                {% endif %}
            </a>
        </nav>
    </div>

    <!-- 中间：搜索功能 -->
    <div class="flex items-center gap-2 flex-1 max-w-xs mx-4 md:max-w-[200px] md:mx-2">
        <div class="relative flex-1">
            <input type="text"
                   id="navSearchInput"
                   placeholder="搜索笔记..."
                   value="{{ request.args.get('q', '') }}"
                   class="w-full px-3 py-2.5 text-sm border border-gray-300 rounded-lg transition-all duration-300 bg-white focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-100 md:px-2 md:py-2 md:text-xs md:placeholder:text-xs" />
        </div>
        <button id="navSearchButton"
                class="px-3 py-2.5 text-sm font-medium bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0 rounded-lg cursor-pointer transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105 active:scale-95 md:px-2 md:py-2 md:text-xs">
            <svg class="w-4 h-4 md:w-3 md:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </button>
    </div>

    <!-- 右侧：退出链接 -->
    <a href="{{ url_for('logout') }}"
       class="group relative text-sm font-medium text-gray-400 no-underline px-4 py-2.5 rounded-xl transition-all duration-300 ease-out hover:text-red-500 hover:bg-red-50 hover:scale-105 md:px-3 md:py-2 md:text-xs">
        <span class="relative z-10">退出</span>
        <div class="absolute inset-0 bg-red-500 rounded-xl opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
    </a>
</div>

<!-- 移动端下拉菜单 -->
<div id="mobileMenu" class="md:hidden fixed top-16 left-1/2 transform -translate-x-1/2 w-full max-w-4xl bg-white border-b border-gray-200 shadow-lg rounded-b-xl z-40 hidden">
    <nav class="flex flex-col py-2" role="tablist">
        <!-- 主页标签 -->
        <a href="{{ url_for('index') }}"
           class="group relative text-sm font-medium no-underline px-6 py-3 transition-all duration-300 ease-out border-b border-gray-100 last:border-b-0
                  {% if active_page == 'index' %}
                    text-white bg-gradient-to-r from-indigo-500 to-purple-600
                  {% else %}
                    text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50
                  {% endif %}"
           role="tab"
           aria-selected="{% if active_page == 'index' %}true{% else %}false{% endif %}">
            <span class="relative z-10 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                主页
            </span>
            {% if active_page != 'index' %}
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            {% endif %}
        </a>

        <!-- 标签页标签 -->
        <a href="{{ url_for('tags') }}"
           class="group relative text-sm font-medium no-underline px-6 py-3 transition-all duration-300 ease-out border-b border-gray-100 last:border-b-0
                  {% if active_page == 'tags' %}
                    text-white bg-gradient-to-r from-indigo-500 to-purple-600
                  {% else %}
                    text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50
                  {% endif %}"
           role="tab"
           aria-selected="{% if active_page == 'tags' %}true{% else %}false{% endif %}">
            <span class="relative z-10 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                标签
            </span>
            {% if active_page != 'tags' %}
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            {% endif %}
        </a>

        <!-- 随机页标签 -->
        <a href="{{ url_for('random_post_page') }}"
           class="group relative text-sm font-medium no-underline px-6 py-3 transition-all duration-300 ease-out border-b border-gray-100 last:border-b-0
                  {% if active_page == 'random' %}
                    text-white bg-gradient-to-r from-indigo-500 to-purple-600
                  {% else %}
                    text-gray-600 hover:text-indigo-600 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50
                  {% endif %}"
           role="tab"
           aria-selected="{% if active_page == 'random' %}true{% else %}false{% endif %}">
            <span class="relative z-10 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                随机
            </span>
            {% if active_page != 'random' %}
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            {% endif %}
        </a>
    </nav>
</div>
