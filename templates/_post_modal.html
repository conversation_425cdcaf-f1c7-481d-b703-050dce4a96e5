<!-- 发布笔记模态框 -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="postModal">
    <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] min-w-[600px] max-w-[80vw] lg:max-w-[1200px] shadow-2xl border border-gray-300 animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
        <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200 flex-shrink-0">
            <h5 class="text-lg font-semibold text-gray-800 m-0">发布笔记</h5>
            <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500 close-button" data-dismiss="modal">&times;</button>
        </div>
        <div class="p-8 flex-1 overflow-y-auto">
            <form id="postForm" class="space-y-6">
                <div class="flex flex-col gap-0 rounded-xl border-2 border-gray-200 overflow-hidden">
                    <!-- 编辑器容器 -->
                    <div class="editor-container">
                        <textarea name="content" id="markdownEditor" placeholder="记录你的想法和灵感..." required
                                  class="w-full min-h-250 md:min-h-300 p-6 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                    </div>
                </div>
                <!-- 标签编辑区域和发布按钮 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <div class="tags-input-wrapper">
                            <div class="tags-container" id="tagsContainer"></div>
                            <input type="text" id="tagInput" placeholder="添加标签"
                                   class="flex-1 min-w-0 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200" />

                            <!-- 发布按钮 -->
                            <div class="flex-shrink-0 ml-2">
                                <button type="submit" form="postForm" class="px-4 py-1.5 bg-indigo-600 text-white font-semibold rounded-lg transition-all duration-300 hover:bg-indigo-700 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-100 text-sm shadow-sm">
                                    发布
                                </button>
                            </div>
                        </div>
                        <input type="hidden" name="tags" id="tagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 发布按钮由各页面自己定义，这里不需要重复定义 -->

<style>
/* 发布笔记模态框样式 - 使用Tailwind CSS */
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 动画效果 */
@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-fade-in {
    animation: modalFadeIn 0.3s ease;
}

.animate-slide-in {
    animation: modalSlideIn 0.3s ease;
}

/* 编辑器容器样式 */
#postModal .editor-container {
    position: relative;
    border: 0;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

/* 标签输入区域样式 */
#postModal .tags-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    transition: border-color 0.3s ease;
}

#postModal .tags-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 移动端编辑器内边距优化 */
@media (max-width: 768px) {
    #postModal textarea {
        padding: 1rem !important;
    }
}

@media (max-width: 480px) {
    #postModal textarea {
        padding: 0.75rem !important;
    }
}

</style>

<script>
// 注意：TagManager类现在使用外部的tags.js文件中的实现

// 发布笔记模态框控制器
class PostModalController {
    constructor(options = {}) {
        this.createPostUrl = options.createPostUrl || '/post/create';
        this.onPostCreated = options.onPostCreated || this.defaultOnPostCreated.bind(this);

        this.init();
    }
    
    init() {
        this.postButton = document.getElementById('postButton');
        this.postModal = document.getElementById('postModal');
        this.postForm = document.getElementById('postForm');

        // 初始化标签管理器
        this.tagManager = new TagManager({
            tagInput: document.getElementById('tagInput'),
            tagsContainer: document.getElementById('tagsContainer'),
            tagsField: document.getElementById('tagsField')
        });

        this.bindEvents();
    }
    
    bindEvents() {
        // 发布按钮点击事件
        this.postButton.addEventListener('click', () => this.showModal());
        
        // 关闭模态框事件
        this.postModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', () => this.hideModal());
        });
        
        // 点击背景关闭（仅通过关闭按钮）
        this.postModal.addEventListener('click', (event) => {
            if (event.target.classList.contains('close-button')) {
                this.hideModal();
            }
        });
        
        // 表单提交事件
        this.postForm.addEventListener('submit', (e) => this.handleSubmit(e));
    }
    
    showModal() {
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        this.postModal.classList.add('show');
    }

    hideModal() {
        document.body.style.overflow = ''; // 恢复滚动
        this.postModal.classList.remove('show');
        this.postForm.reset();
        this.tagManager.clearTags();
    }
    
    handleSubmit(e) {
        e.preventDefault();
        
        // 检查未添加的标签
        this.tagManager.processRemainingTag();
        
        const formData = new FormData(this.postForm);
        formData.append('tags', JSON.stringify(this.tagManager.getTags()));
        
        fetch(this.createPostUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    try {
                        const errorData = JSON.parse(text);
                        throw new Error(errorData.message || '请求失败');
                    } catch {
                        throw new Error(`服务器错误 (${response.status}): ${text.slice(0, 100)}`);
                    }
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.onPostCreated(data.post);
            } else {
                alert('发布失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发布失败，请重试');
        })
        .finally(() => {
            this.hideModal();
        });
    }
    
    defaultOnPostCreated(post) {
        // 尝试动态添加新笔记到页面，如果失败则刷新页面
        try {
            // 创建新的微博卡片
            const postCard = document.createElement('div');
            postCard.className = 'card bg-white rounded-2xl p-8 shadow-sm border border-gray-200 relative overflow-hidden md:p-6';
            postCard.setAttribute('data-post-id', post.id);

            // 构建微博卡片内容
            let tagsHtml = '';
            if (post.tags && post.tags.length > 0) {
                post.tags.forEach(tag => {
                    const isActive = post.current_tag === tag ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/25 scale-105' : '';
                    const baseClasses = 'group relative inline-flex items-center px-3 py-1.5 text-xs font-medium no-underline rounded-full transition-all duration-300 ease-out';
                    const styleClasses = isActive ? 
                        'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/25 scale-105' :
                        'bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 border border-indigo-200 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 hover:border-indigo-300 hover:scale-105 hover:shadow-md hover:shadow-indigo-500/10 hover:-translate-y-0.5';
                    tagsHtml += `<a href="/?tag=${encodeURIComponent(tag)}" class="${baseClasses} ${styleClasses}">${tag}</a>`;
                });
            }

            postCard.innerHTML = `
                <div class="card-header mb-0">
                    <div class="header-row flex items-center justify-between mb-4">
                        <div class="post-time text-xs text-gray-500 font-medium">${post.created_at}</div>
                        <div class="btn-group flex gap-2">
                            <button class="btn-secondary px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-indigo-600 text-white hover:bg-indigo-700 hover:-translate-y-0.5 edit-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="${post.id}">编辑</button>
                            <button class="btn-danger px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-500 text-white hover:bg-red-600 hover:-translate-y-0.5 delete-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="${post.id}">删除</button>
                        </div>
                    </div>
                    <div class="post-content text-base leading-relaxed text-gray-800 mb-6 break-words markdown-content" id="content-${post.id}">${post.rendered_content}</div>
                    <div class="tags-container flex flex-wrap gap-2">
                        ${tagsHtml}
                    </div>
                </div>
            `;

            // 将新微博添加到列表顶部
            const postsContainer = document.querySelector('.posts-list');
            if (postsContainer) {
                const firstCard = postsContainer.querySelector('.card');
                if (firstCard) {
                    postsContainer.insertBefore(postCard, firstCard);
                } else {
                    postsContainer.appendChild(postCard);
                }
            } else {
                // 如果没有posts-list容器，则添加到container中分页导航之前
                const container = document.querySelector('.container');
                const pagination = container.querySelector('.pagination');
                if (pagination) {
                    container.insertBefore(postCard, pagination);
                } else {
                    container.appendChild(postCard);
                }
            }
        } catch (error) {
            console.error('动态添加笔记失败:', error);
            // 如果动态添加失败，则刷新页面
            window.location.reload();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在必要的元素
    if (document.getElementById('postButton') && document.getElementById('postModal')) {
        // 创建发布笔记控制器
        window.postModalController = new PostModalController({
            createPostUrl: window.API_URLS?.createPost || '/post/create',
            onPostCreated: window.onPostCreated || undefined
        });
    }
});
</script>
