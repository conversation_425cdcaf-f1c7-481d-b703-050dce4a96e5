<!-- 编辑笔记对话框 -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="editModal">
    <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] min-w-[600px] max-w-[80vw] lg:max-w-[1200px] shadow-2xl border border-gray-300 animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200 flex-shrink-0">
                <div class="flex items-center gap-3">
                    <input type="datetime-local" id="editPostTime" name="editPostTime" form="editForm"
                           class="px-2 py-1 text-sm border border-slate-300 rounded-md transition-all duration-200 bg-white text-slate-600 focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 w-auto" style="min-width: 180px;">
                    <button type="button" id="setCurrentTimeBtn" class="px-3 py-1 text-sm font-semibold bg-indigo-500 text-white rounded-md cursor-pointer transition-all duration-200 whitespace-nowrap shadow-sm hover:bg-indigo-600 hover:-translate-y-px">现在</button>
                </div>
                <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500 close-button" data-dismiss="modal">&times;</button>
            </div>
        <div class="p-8 flex-1 overflow-y-auto">
            <form id="editForm" class="space-y-6">
                <div class="flex flex-col gap-0 rounded-xl border-2 border-gray-200 overflow-hidden">
                    <!-- 编辑器容器 -->
                    <div class="editor-container">
                        <textarea name="content" id="editMarkdownEditor" placeholder="支持Markdown格式，编辑笔记内容" required
                                  class="w-full min-h-250 md:min-h-300 p-6 border-none font-mono resize-y bg-white focus:outline-none" style="font-size: 15px;"></textarea>
                    </div>
                </div>
                <!-- 标签编辑区域和保存按钮 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <div class="group relative flex items-center flex-wrap gap-2 p-3 border-2 border-gray-200 rounded-xl bg-white min-h-[56px] transition-all duration-300 focus-within:border-indigo-500 focus-within:ring-4 focus-within:ring-indigo-100 focus-within:shadow-sm md:min-h-[50px] md:p-2.5">
                            <!-- 标签容器 -->
                            <div class="flex flex-wrap gap-2" id="editTagsContainer"></div>
                            
                            <!-- 标签输入框 -->
                            <input type="text"
                                   id="editTagInput"
                                   placeholder="添加标签..."
                                   class="flex-1 min-w-[120px] border-0 outline-0 p-1 text-sm bg-transparent text-gray-700 placeholder-gray-400 md:min-w-[100px]" />
                            
                            <!-- 保存按钮 -->
                            <div class="flex-shrink-0 ml-2">
                                <button type="submit" form="editForm" class="px-4 py-1.5 bg-indigo-600 text-white font-semibold rounded-lg transition-all duration-300 hover:bg-indigo-700 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-100 text-sm shadow-sm">
                                    保存
                                </button>
                            </div>
                        </div>
                        <input type="hidden" name="tags" id="editTagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 编辑模态框样式 */
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}


/* 在小屏幕设备上，将标签和按钮垂直排列 */
@media (max-width: 768px) {
    #editModal .flex.items-center.justify-between.gap-4 {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    #editModal .flex-shrink-0 button {
        width: 100%;
        justify-content: center;
    }
}

/* 移动端编辑器内边距优化 */
@media (max-width: 768px) {
    #editModal textarea {
        padding: 1rem !important;
    }
}

@media (max-width: 480px) {
    #editModal textarea {
        padding: 0.75rem !important;
    }
}

/* 编辑器容器样式 */
#editModal .editor-container {
    position: relative;
    border: 0;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

</style>

<script>
// 移动端虚拟键盘处理
class MobileKeyboardHandler {
    constructor() {
        this.modal = document.getElementById('editModal');
        this.originalViewportHeight = window.innerHeight;
        this.init();
    }

    init() {
        if (!this.modal) return;

        // 监听窗口大小变化（虚拟键盘弹出/收起）
        window.addEventListener('resize', () => this.handleResize());

        // 监听输入框焦点事件
        const inputs = this.modal.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', () => this.handleInputFocus());
            input.addEventListener('blur', () => this.handleInputBlur());
        });
    }

    handleResize() {
        const currentHeight = window.innerHeight;
        const heightDiff = this.originalViewportHeight - currentHeight;

        // 如果高度减少超过150px，认为是虚拟键盘弹出
        if (heightDiff > 150) {
            this.adjustModalForKeyboard(true);
        } else {
            this.adjustModalForKeyboard(false);
        }
    }

    handleInputFocus() {
        // 延迟执行，等待虚拟键盘完全弹出
        setTimeout(() => {
            this.scrollToActiveInput();
        }, 300);
    }

    handleInputBlur() {
        // 虚拟键盘收起时恢复原始状态
        setTimeout(() => {
            this.adjustModalForKeyboard(false);
        }, 300);
    }

    adjustModalForKeyboard(keyboardVisible) {
        const modalContent = this.modal.querySelector('.bg-white');
        if (!modalContent) return;

        if (keyboardVisible) {
            modalContent.style.maxHeight = '70vh';
            modalContent.style.margin = '1% auto';
        } else {
            modalContent.style.maxHeight = '';
            modalContent.style.margin = '';
        }
    }

    scrollToActiveInput() {
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
            activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}

// 初始化移动端处理
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('editModal')) {
        window.mobileKeyboardHandler = new MobileKeyboardHandler();
    }
});
</script>
