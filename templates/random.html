<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>随机笔记</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- 引入 Tailwind CSS -->
    <link href="/static/css/tailwind/tailwind.min.css" rel="stylesheet">
    <!-- 引入 Tailwind 自定义补充样式 -->
    <link href="/static/css/tailwind-custom.css" rel="stylesheet">
    <!-- 引入公共标签样式 -->
    <link rel="stylesheet" href="/static/css/tags.css">
    <!-- 引入Markdown公共样式 -->
    <link rel="stylesheet" href="/static/css/markdown.css">
</head>
<body>
    <!-- 固定导航栏 -->
    {% set active_page = 'random' %}
    {% include '_navbar.html' %}

    <!-- 主容器 -->
    <div class="max-w-4xl mx-auto px-8 py-8 mt-20 md:px-4 md:mt-16">
        <div id="post-display-area" class="mb-12">
            {% if post %}
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in md:p-6" data-post-id="{{ post.id }}">
                 <div>
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                        <span class="text-sm text-gray-500 font-medium">{{ post.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        <div class="flex gap-2">
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post" data-post-id="{{ post.id }}">编辑</button>
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post" data-post-id="{{ post.id }}">删除</button>
                        </div>
                    </div>
                    <div class="post-content leading-relaxed text-gray-800 mb-8 break-words markdown-content" id="content-{{ post.id }}">{{ post.rendered_content|safe }}</div>
                </div>
                {% if post.tags %}
                <div class="tags-container mt-6 pt-4 border-t border-gray-200 flex flex-wrap gap-2">
                    {% for tag in post.tags or [] %} {# 添加 or [] 防止 tags 为 None #}
                    <a href="{{ url_for('index', tag=tag) }}"
                       class="note-tag note-tag--normal">
                        <span class="tag-text">{{ tag }}</span>
                    </a>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% else %}
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in">
                <div class="text-base leading-relaxed text-gray-500 text-center italic">没有找到任何笔记。</div>
            </div>
            {% endif %}
        </div>

        <!-- 随机一条按钮 -->
        <div class="flex justify-center mt-12 mb-8">
            <button id="random-again-btn"
                    class="flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl cursor-pointer transition-colors duration-200 bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                随机一条
            </button>
        </div>
    </div>

    <!-- 引入编辑笔记对话框 -->
    {% include '_edit_modal.html' %}

    <!-- 删除确认对话框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="deleteConfirmModal">
        <div class="bg-white my-[5%] mx-auto rounded-2xl w-[90%] max-w-md shadow-2xl animate-slide-in overflow-hidden">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-red-50/50 to-pink-50/50 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-800 m-0">确认删除</h5>
                <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" data-dismiss="modal">&times;</button>
            </div>
            <div class="p-8">
                <p class="text-gray-700 text-base m-0">确定要删除这条微博吗？</p>
            </div>
            <div class="px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4">
                <button type="button" class="px-6 py-2 text-sm font-semibold bg-gray-100 text-gray-600 border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm" data-dismiss="modal">取消</button>
                <form id="deletePostForm" method="POST" class="inline">
                    <button type="submit" class="px-6 py-2 text-sm font-semibold bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 shadow-sm hover:-translate-y-1 hover:shadow-md">删除</button>
                </form>
            </div>
        </div>
    </div>

    <!-- 悬浮发布按钮 -->
    <button id="postButton"
            class="fixed bottom-8 right-8 w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/30 border-0 cursor-pointer flex items-center justify-center transition-all duration-300 ease-out z-40 hover:shadow-xl hover:shadow-indigo-500/40 hover:scale-110 active:scale-95 md:bottom-6 md:right-6 md:w-12 md:h-12">
        <svg class="w-6 h-6 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
    </button>

    <script>
        window.API_URLS = {
            createPost: '{{ url_for("create_post") }}',
            updatePost: '{{ url_for("update_post", post_id=0) }}'
        };
        
        // 发布按钮点击事件 - 跳转到主页并打开发布模态框
        document.addEventListener('DOMContentLoaded', function() {
            const postButton = document.getElementById('postButton');
            if (postButton) {
                postButton.addEventListener('click', function() {
                    // 跳转到主页并添加参数来指示打开发布模态框
                    window.location.href = '{{ url_for("index") }}?openPost=1';
                });
            }
        });
    </script>
    <script src="/static/js/search.js"></script>
    <script src="/static/js/tags.js"></script>
    <script src="/static/js/random.js"></script>
    <script src="/static/js/navbar.js"></script>
</body>
</html>
