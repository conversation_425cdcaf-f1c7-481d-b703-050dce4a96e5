# 变更日志

## [Unreleased]

### 功能
- 

### 修复
- 

### 改进
- 统一 Markdown 编辑器工具栏和编辑器容器的边框样式，将边框移到父容器上
- 简化标签编辑界面，删除冗余的提示文字和图标元素，提升用户体验
- 添加 .clineignore 文件，配置 Cline 忽略规则
- 修改发布笔记的提示语为"记录你的想法和灵感..."，优化用户体验
- 清理 tailwind-custom.css 中重复的 Markdown 样式，避免样式冲突
- 将 random.css 中的 blockquote 样式合并到公共 markdown.css 文件中，统一样式管理
- 删除过时的 login.css 文件，保持项目整洁
- 重构 random.html 页面，完全使用 Tailwind CSS 替代 random.css，移除对随机页面专用样式文件的依赖
- 删除随机笔记界面的「上一条」和「下一条」功能，简化用户界面

## [1.0.0] - 2025-08-02

### 功能
- 初始化项目基础框架
- 实现微博发布、编辑、删除功能
- 添加标签管理和搜索功能
- 实现随机浏览微博功能
- 添加文件上传功能

### 修复
- 

### 改进
-
